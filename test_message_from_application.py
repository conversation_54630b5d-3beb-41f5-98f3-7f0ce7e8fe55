#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试从申请管理发送站内信功能
"""

import requests
import json

def test_send_message_from_application():
    """测试从申请管理发送站内信"""
    
    # 服务器地址
    base_url = "http://localhost:7799"
    
    # 登录管理员账户
    login_data = {
        "username": "admin",
        "password": "admin123"  # 假设这是管理员密码
    }
    
    session = requests.Session()
    
    # 登录
    login_response = session.post(f"{base_url}/login", data=login_data)
    print(f"登录响应状态码: {login_response.status_code}")
    
    if login_response.status_code != 200:
        print("登录失败")
        return False
    
    # 测试发送站内信
    message_data = {
        "recipient": "admin",  # 发送给admin用户
        "title": "关于您的点数申请",
        "content": "您好，关于您提交的点数申请，我们已经审核完毕。如有任何疑问，请随时联系我们。"
    }
    
    # 发送站内信
    send_response = session.post(
        f"{base_url}/admin/messages/send",
        json=message_data,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"发送站内信响应状态码: {send_response.status_code}")
    
    if send_response.status_code == 200:
        response_data = send_response.json()
        print(f"发送结果: {response_data}")
        
        if response_data.get('success'):
            print("✅ 站内信发送成功！")
            return True
        else:
            print(f"❌ 站内信发送失败: {response_data.get('message')}")
            return False
    else:
        print(f"❌ 请求失败，状态码: {send_response.status_code}")
        return False

def test_get_messages():
    """测试获取消息列表"""
    
    base_url = "http://localhost:7799"
    session = requests.Session()
    
    # 登录
    login_data = {"username": "admin", "password": "admin123"}
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code != 200:
        print("登录失败")
        return False
    
    # 获取消息列表
    messages_response = session.get(f"{base_url}/api/messages")
    
    print(f"获取消息列表响应状态码: {messages_response.status_code}")
    
    if messages_response.status_code == 200:
        response_data = messages_response.json()
        print(f"消息数量: {len(response_data.get('messages', []))}")
        print(f"未读消息数量: {response_data.get('unread_count', 0)}")
        
        # 显示最新的几条消息
        messages = response_data.get('messages', [])
        if messages:
            print("\n最新消息:")
            for i, msg in enumerate(messages[:3]):  # 显示前3条
                print(f"{i+1}. 标题: {msg['title']}")
                print(f"   发送者: {msg['sender']}")
                print(f"   时间: {msg['timestamp']}")
                print(f"   已读: {'是' if msg['is_read'] else '否'}")
                print()
        
        return True
    else:
        print(f"❌ 获取消息失败，状态码: {messages_response.status_code}")
        return False

if __name__ == "__main__":
    print("🧪 开始测试从申请管理发送站内信功能...")
    print()
    
    # 测试发送消息
    print("1. 测试发送站内信...")
    send_success = test_send_message_from_application()
    print()
    
    # 测试获取消息
    print("2. 测试获取消息列表...")
    get_success = test_get_messages()
    print()
    
    # 总结
    if send_success and get_success:
        print("✅ 所有测试通过！从申请管理发送站内信功能正常工作。")
    else:
        print("❌ 部分测试失败，请检查功能实现。")
