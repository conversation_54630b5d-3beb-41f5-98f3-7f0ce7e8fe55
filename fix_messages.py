#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复消息数据，添加 read_by 字段
"""

import json
import re

def fix_messages_json():
    """修复 messages.json 文件，为每条消息添加 read_by 字段"""
    
    # 读取文件内容
    with open('messages.json', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用正则表达式在每个消息对象的最后添加 read_by 字段
    # 匹配模式：在 "read_at": null 或 "read_at": "..." 后面添加 read_by 字段
    pattern = r'("read_at":\s*(?:null|"[^"]*"))\s*\n(\s*})'
    replacement = r'\1,\n\2  "read_by": {}\n\2'
    
    # 执行替换
    new_content = re.sub(pattern, replacement, content)
    
    # 保存修改后的内容
    with open('messages.json', 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("已为所有消息添加 read_by 字段")

if __name__ == '__main__':
    fix_messages_json()
