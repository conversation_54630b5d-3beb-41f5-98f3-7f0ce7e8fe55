#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息数据迁移脚本
将旧的消息数据格式迁移到新的格式，支持每个用户独立的读取状态
"""

import json
import os
from datetime import datetime

def migrate_messages_data():
    """迁移消息数据"""
    data_file = 'messages.json'
    backup_file = 'messages_backup.json'
    
    if not os.path.exists(data_file):
        print("消息数据文件不存在，无需迁移")
        return
    
    try:
        # 读取现有数据
        with open(data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 创建备份
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"已创建备份文件: {backup_file}")
        
        # 迁移消息数据
        migrated_count = 0
        for message in data.get('messages', []):
            # 如果消息还没有 read_by 字段，则添加
            if 'read_by' not in message:
                message['read_by'] = {}
                
                # 如果消息已被标记为已读，且是发给特定用户的
                if message.get('is_read', False) and message.get('recipient') != 'all':
                    # 将读取状态迁移到新格式
                    recipient = message.get('recipient')
                    if recipient:
                        message['read_by'][recipient] = True
                
                migrated_count += 1
        
        # 保存迁移后的数据
        with open(data_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"迁移完成！共处理 {migrated_count} 条消息")
        print("新的消息格式支持每个用户独立的读取状态")
        
    except Exception as e:
        print(f"迁移失败: {e}")
        # 如果迁移失败，尝试恢复备份
        if os.path.exists(backup_file):
            try:
                with open(backup_file, 'r', encoding='utf-8') as f:
                    backup_data = json.load(f)
                with open(data_file, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)
                print("已恢复备份数据")
            except:
                print("恢复备份失败，请手动检查数据文件")

if __name__ == '__main__':
    migrate_messages_data()
