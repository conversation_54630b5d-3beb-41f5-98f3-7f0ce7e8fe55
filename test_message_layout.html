<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>站内信布局测试</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        /* 站内信未读消息徽章增强样式 */
        .message-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            min-width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff4757, #ff3742);
            color: white;
            font-size: 11px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);
            border: 2px solid white;
            z-index: 1000;
            animation: messageNotification 2s ease-in-out infinite;
        }
        
        .message-badge.pulse {
            animation: messagePulse 1.5s ease-in-out infinite;
        }
        
        @keyframes messageNotification {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); box-shadow: 0 4px 12px rgba(255, 71, 87, 0.6); }
        }
        
        @keyframes messagePulse {
            0%, 100% { 
                transform: scale(1); 
                box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);
            }
            50% { 
                transform: scale(1.15); 
                box-shadow: 0 4px 16px rgba(255, 71, 87, 0.8);
            }
        }
        
        /* 站内信按钮增强样式 */
        .message-btn {
            position: relative;
            transition: all 0.3s ease;
        }
        
        .message-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        
        .message-btn.has-unread {
            border-color: #ff4757 !important;
            color: #ff4757 !important;
        }
        
        .message-btn.has-unread:hover {
            background-color: #ff4757 !important;
            color: white !important;
        }
        
        /* 按钮组样式调整 */
        .btn-group .message-btn:first-child {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }
        
        .btn-group .message-btn:last-child {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-left: none;
        }
        
        .btn-group .message-btn.has-unread:last-child {
            border-left: 1px solid #ff4757;
        }
        
        /* 消息提示工具提示 */
        .message-tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1001;
            margin-bottom: 5px;
        }
        
        .message-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.8);
        }
        
        .message-btn:hover .message-tooltip {
            opacity: 1;
            visibility: visible;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>站内信布局测试</h2>
        
        <div class="card mb-4">
            <div class="card-body">
                <h5>当前布局（分离式按钮组）</h5>
                <div class="d-flex justify-content-end">
                    <div class="btn-group me-2">
                        <a href="/messages" class="btn btn-outline-info btn-sm message-btn has-unread">
                            <i class="fas fa-envelope me-1"></i>站内信
                            <span class="message-badge pulse">
                                <span>5</span>
                            </span>
                            <div class="message-tooltip">
                                您有 5 条未读消息
                            </div>
                        </a>
                        <button type="button" class="btn btn-outline-info btn-sm dropdown-toggle dropdown-toggle-split message-btn has-unread" 
                                data-bs-toggle="dropdown" aria-expanded="false">
                            <span class="visually-hidden">切换下拉菜单</span>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end">
                            <div class="dropdown-header">消息预览</div>
                            <div class="dropdown-item">测试消息 1</div>
                            <div class="dropdown-item">测试消息 2</div>
                        </div>
                    </div>
                    <button class="btn btn-outline-secondary btn-sm">退出登录</button>
                </div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-body">
                <h5>原始布局（单个按钮）</h5>
                <div class="d-flex justify-content-end">
                    <a href="/messages" class="btn btn-outline-info btn-sm me-2 message-btn has-unread">
                        <i class="fas fa-envelope me-1"></i>站内信
                        <span class="message-badge pulse">
                            <span>5</span>
                        </span>
                        <div class="message-tooltip">
                            您有 5 条未读消息
                        </div>
                    </a>
                    <button class="btn btn-outline-secondary btn-sm">退出登录</button>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body">
                <h5>控制面板</h5>
                <button class="btn btn-primary btn-sm me-2" onclick="toggleUnread()">切换未读状态</button>
                <button class="btn btn-secondary btn-sm" onclick="toggleAnimation()">切换动画</button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleUnread() {
            const badges = document.querySelectorAll('.message-badge');
            const btns = document.querySelectorAll('.message-btn');
            
            badges.forEach(badge => {
                badge.classList.toggle('d-none');
            });
            
            btns.forEach(btn => {
                btn.classList.toggle('has-unread');
            });
        }
        
        function toggleAnimation() {
            const badges = document.querySelectorAll('.message-badge');
            badges.forEach(badge => {
                badge.classList.toggle('pulse');
            });
        }
    </script>
</body>
</html>
